package ui

import (
	"deskcrm/api/arkgo"
	"deskcrm/api/dal"
	"deskcrm/api/dat"
	"deskcrm/api/dau"
	"deskcrm/api/mesh"
	"deskcrm/components"
	"deskcrm/components/define"
	"deskcrm/consts"
	"deskcrm/controllers/http/innerapi/input/inputArk"
	"deskcrm/controllers/http/ui/input/inputStudent"
	"deskcrm/controllers/http/ui/output/outputStudent"
	"deskcrm/models"
	"deskcrm/service/innerapi/survey"
	"deskcrm/util"
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	json "github.com/json-iterator/go"
	"github.com/spf13/cast"
)

type studentService struct{}

var (
	StudentService studentService
)

// 获取学生list
func (s studentService) GetStudentList(ctx *gin.Context, param *inputStudent.StudentListParam) (rsp outputStudent.StudentListOutput, err error) {
	// param
	if param == nil {
		err = components.InvalidParam("param is nil")
		return
	}

	defer func() {
		if err != nil {
			zlog.Errorf(ctx, "GetStudentList error, err:%v", err)
		}

		if errPanic := recover(); errPanic != nil {
			zlog.Errorf(ctx, "GetStudentList panic, err:%v", errPanic)
			components.Util.PanicTrace(ctx)
		}
	}()

	// query: 方舟
	rspData, err := arkgo.NewClient().PostForwardRequest(ctx, arkgo.ArkListApi, components.StructToMap(ctx, transformParm4ArkList(param)))
	if err != nil || rspData == nil {
		return
	}

	var arkList arkgo.ArkListRsp
	err = json.Unmarshal([]byte(cast.ToString(rspData)), &arkList)
	if err != nil || arkList.Total == 0 {
		return outputStudent.StudentListOutput{}, err
	}
	// query: 学生list
	studentList, err := getBaseInfo(ctx, *param)
	if err != nil {
		return outputStudent.StudentListOutput{}, err
	}
	components.Debugf(ctx, "GetStudentList getBaseInfo,%+v,%+v", param, studentList)

	studentList.List = arkList.StudentList
	// studentList.FieldMapTree = arkList.FieldMapTree 此字段已废弃，由 getFieldMapTree 接口单独返回
	studentList.Total = arkList.Total
	studentList.SelectedCnt = arkList.FilterStudentCnt
	studentList.AllStudentsIDs = arkList.FilterStudentUids
	return studentList, nil
}

// 获取返回的基础信息, course、teacher等信息
func getBaseInfo(ctx *gin.Context, param inputStudent.StudentListParam) (outputStudent.StudentListOutput, error) {
	// param
	assistantUid := param.AssistantUid
	courseId := param.CourseId
	strCourseId := cast.ToString(courseId)
	lessonId := param.LessonId

	// query: dal, 获取课程章节信息
	id2Info, err := dal.GetCourseLessonInfoByCourseIds(ctx, []int64{courseId})
	if err != nil {
		return outputStudent.StudentListOutput{}, err
	}
	courseInfo, exist := id2Info[strconv.FormatInt(courseId, 10)]
	if !exist {
		return outputStudent.StudentListOutput{}, fmt.Errorf("课程信息不存在")
	}

	// 找到课程下的章节信息
	var lessonInfo dal.LessonInfo
	if lessonId > 0 && len(courseInfo.LessonList) > 0 {
		for _, info := range courseInfo.LessonList {
			if info.LessonId == int(lessonId) { // lessonId定义各处不统一
				lessonInfo = info
				break
			}
		}
	}
	// 获取辅导老师信息
	assistantInfo, err := mesh.NewClient().GetUserInfoByDeviceUid(ctx, assistantUid)
	if err != nil {
		return outputStudent.StudentListOutput{}, err
	}

	// 获取主讲老师name
	courseId2teacherNameList, err := GetTeacherNameListByCourseIds(ctx, []int64{courseId}, []string{"teacherName"})
	if err != nil {
		return outputStudent.StudentListOutput{}, err
	}

	// 返回数据
	var res outputStudent.StudentListOutput
	res.AssistantUID = assistantUid
	res.CourseName = courseInfo.CourseName
	res.CourseType = courseInfo.CourseType
	res.OnlineTime = courseInfo.OnlineFormatTimeAll
	res.TeacherName = strings.Join(courseId2teacherNameList[strCourseId], consts.SeparatorComma)
	res.AssistantPhone = assistantInfo.Record.Phone
	if util.IsHitGray(ctx, param.PersonUid, util.HideSensitivePhoneMercuryKey) {
		res.AssistantPhone = components.Util.HiddenPhone(assistantInfo.Record.Phone)
	}

	xbId := define.Grade2XB[int(courseInfo.MainGradeId)] // 学部id
	res.GradeStage = xbId

	var isLessonFinish int
	if lessonInfo.StopTime > 0 && int64(lessonInfo.StopTime) <= time.Now().Unix() { // 已结束
		isLessonFinish = 1
	} else {
		isLessonFinish = 0
	}
	res.IsLessonFinish = isLessonFinish

	res.SubjectID = int(courseInfo.MainSubjectId) // 类型经常不统一, 以哪个为准?
	res.GradeID = int(courseInfo.MainGradeId)
	res.NewCourseType = int(courseInfo.NewCourseType)

	surveyQueCond := map[string]interface{}{
		"year":         courseInfo.Year,
		"season":       courseInfo.Season,
		"type":         survey.TYPE_1,
		"department":   xbId,
		"subject":      courseInfo.MainSubjectId,
		"grade":        courseInfo.MainGradeId,
		"assistantUid": assistantUid,
		"courseId":     courseId,
	}

	// 此处的问卷看来已经废弃了，最新的记录是2022年的，可以考虑下线
	res.QueID, err = survey.GetBindSurveyQueId(ctx, surveyQueCond) // 获取调查问卷id
	if err != nil {
		return res, err
	}
	return res, nil
}

// 转化为arkList参数
func transformParm4ArkList(param *inputStudent.StudentListParam) *inputArk.ArkListParam {
	if param == nil {
		return &inputArk.ArkListParam{}
	}
	// transform
	return &inputArk.ArkListParam{
		AssistantUid:    param.AssistantUid,
		PersonUid:       param.PersonUid,
		CourseId:        param.CourseId,
		LessonId:        param.LessonId,
		TplId:           param.TplId,
		TaskId:          param.TaskId,
		ServiceId:       param.ServiceId,
		Timestamp:       param.Timestamp,
		Keyword:         param.Keyword,
		Sorts:           param.Sorts,
		Filter:          param.Filter,
		DataRangeSelect: param.DataRangeSelect,
		Offset:          param.Pn * param.Rn,
		Limit:           param.Rn,
	}
}

// 根据课程id获取老师信息
// return: courseId => TeacherNameList
func GetTeacherNameListByCourseIds(ctx *gin.Context, courseIds []int64, fields []string) (map[string][]string, error) {
	if len(courseIds) == 0 {
		return nil, fmt.Errorf("param:[courseIds] is empty]")
	}
	// 获取teacherUid list
	courseId2teacherUidList, err := dat.GetTeachersByCourseIds(ctx, courseIds, fields)
	if err != nil {
		return nil, err
	}

	// 获取teacher信息
	teacherUidList := getTeacherUidList(courseId2teacherUidList) // 合并teacherUid list
	teacherUid2Info, err := dau.GetTeachersByUids(ctx, teacherUidList, fields)
	if err != nil {
		return nil, err
	}

	// 返回数据
	result := make(map[string][]string) // courseId => TeacherNameList
	for curCourseId, curTeacherUidList := range courseId2teacherUidList {
		var teacherNameList []string
		for _, teacherUid := range curTeacherUidList.TeacherUids {
			teacherNameList = append(teacherNameList, teacherUid2Info[teacherUid].TeacherName)
		}
		result[curCourseId] = teacherNameList
	}
	return result, nil
}

// 合并去重返回 teacherUid list
func getTeacherUidList(courseId2teacherUidList map[string]dat.TeacherUidList) []int {
	seen := make(map[int]bool) // 用于去重 teacherUid
	var teacherUidList []int

	// 遍历
	for _, curUidList := range courseId2teacherUidList {
		for _, uid := range curUidList.TeacherUids {
			// 若未出现, 则添加
			if !seen[uid] { // not exist
				seen[uid] = true
				teacherUidList = append(teacherUidList, uid)
			}
		}
	}
	return teacherUidList
}

// GetWxBindInfo 获取微信绑定信息
func (s studentService) GetWxBindInfo(ctx *gin.Context, param *inputStudent.GetWxBindInfoParam) (rsp outputStudent.GetWxBindInfoOutput, err error) {
	// 初始化返回值
	rsp = outputStudent.GetWxBindInfoOutput{
		IsBind: 0,
		IsShow: 0,
	}

	// 检查是否显示微信按钮
	courseList, err := models.TblShowWxCourseDao.GetListByCourseId(ctx, param.CourseId, models.ShowWxCourseStatusValid)
	if err != nil {
		zlog.Warnf(ctx, "query show_wx_course failed, courseId:%d, err:%v", param.CourseId, err)
	}
	if len(courseList) > 0 {
		rsp.IsShow = 1
	}

	// 获取手动标记的微信绑定数据
	bindDataList, err := models.TblWechatBindTempEnterpriseDao.GetByUid(ctx, param.AssistantUid, param.StudentUid)
	if err != nil {
		zlog.Warnf(ctx, "get manual bind data failed, assistantUid:%d, studentUid:%d, err:%v",
			param.AssistantUid, param.StudentUid, err)
	}
	if len(bindDataList) > 0 {
		rsp.IsBind = bindDataList[0].IsBind
	}

	return rsp, nil
}
