package allocate

type NormalLeadsByCourseAssistantResp struct {
	List []*NormalLeadsByCourseAssistant `json:"list"`
}

type NormalLeadsByCourseAssistant struct {
	StudentUid int64  `json:"studentUid"`
	LeadsId    int64  `json:"leadsId"`
	CourseId   int64  `json:"CourseId"`
	AllocTime  int64  `json:"allocTime"`
	ExtData    string `json:"extData"`
}

type NormalLeadsByCourseAssistantExtData struct {
	ScRemark     string
	ScRemarkTime int
	StudentUid   int64 `json:"studentUid"`
	LeadsId      int64 `json:"leadsId"`
	AllocTime    int64 `json:"allocTime"`
}

const (
	LeadsInfoStatusInvalid = 3
	LeadsretrieveType      = 100007
)

type LeadsInfo struct {
	LeadsId       int64  `json:"courseId"`
	PersonUid     int64  `json:"personUid"`
	ClassId       int64  `json:"classId"`
	StudentUid    int64  `json:"studentUid"`
	Grade         int64  `json:"grade"`
	Subject       int64  `json:"subject"`
	WxMapId       int64  `json:"wxMapId"`
	Status        int64  `json:"status"`
	InvalidReason string `json:"invalidReason"`
}

type NoCourseLeadsInfo struct {
	StudentUid int64 `json:"studentUid"`
	CourseId   int64 `json:"courseId"`
	LeadsId    int64 `json:"leadsId"`
	AllocTime  int64 `json:"allocTime"`
	ExpireTime int64 `json:"expireTime"`
	ActivityId int64 `json:"activityId"`
}
type NoCourseLeadsResp struct {
	List []NoCourseLeadsInfo `json:"list"`
}

type NoClassLeadsResp struct {
	List []*NoClassLeads `json:"list"`
}

type NoClassLeads struct {
	StudentUid int64  `json:"studentUid"`
	LeadsId    int64  `json:"leadsId"`
	CourseId   int64  `json:"CourseId"`
	AllocTime  int64  `json:"allocTime"`
	ExtData    string `json:"extData"`
}

// GetLeadsByBatchCourseIdUid 相关结构体
type BatchCourseIdUidParam struct {
	StuUid   int64 `json:"stuUid"`
	CourseId int64 `json:"courseId"`
}

type BatchLeadsInfo struct {
	LeadsId       int64  `json:"leadsId"`
	StudentUid    int64  `json:"studentUid"`
	CourseId      int64  `json:"courseId"`
	AllocTime     int64  `json:"allocTime"`
	Grade         int64  `json:"grade"`
	Subject       int64  `json:"subject"`
	PersonUid     int64  `json:"personUid"`
	Status        int64  `json:"status"`
	ClassId       int64  `json:"classId"`
	StudentType   int64  `json:"studentType"`
	TransferType  int64  `json:"transferType"`
	IsOriginal    int64  `json:"isOriginal"`
	TradeTime     int64  `json:"tradeTime"`
	RefundTime    int64  `json:"refundTime"`
	StudentArea   int64  `json:"studentArea"`
	AssistantArea int64  `json:"assistantArea"`
	InviterUid    int64  `json:"inviterUid"`
	ExtData       string `json:"extData"`
	UserId        int64  `json:"userId"`
	WxMapId       int64  `json:"wxMapId"`
	SellChannelId int64  `json:"sellChannelId"`
	BuyType       int64  `json:"buyType"`
	ChannelId     int64  `json:"channelId"`
	SaleMode      int64  `json:"saleMode"`
	ExpireTime    int64  `json:"expireTime"`
	InvalidReason string `json:"invalidReason"`
	IsRealAlloc   int64  `json:"isRealAlloc"`
	IsDeleted     int64  `json:"isDeleted"`
	Phone         int64  `json:"phone"`
	PhoneStr      string `json:"phoneStr"`
	ActivityId    int64  `json:"activityId"`
	Source        int64  `json:"source"`
	Level         int64  `json:"level"`
	Score         int64  `json:"score"`
	LastFrom      string `json:"lastFrom"`
	Stage         int64  `json:"stage"`
	SubTradeId    int64  `json:"subTradeId"`
	LeadsOrigin   int64  `json:"leadsOrigin"`
	FromPersonUid int64  `json:"fromPersonUid"`
	FromUserId    int64  `json:"fromUserId"`
	FromWxMapId   int64  `json:"fromWxMapId"`
	ActiveStatus  int64  `json:"activeStatus"`
	ActiveTime    int64  `json:"activeTime"`
	TransferTime  int64  `json:"transferTime"`
	CreateTime    int64  `json:"createTime"`
	UpdateTime    int64  `json:"updateTime"`
	IsGrey        bool   `json:"isGrey"`
	IsValid       bool   `json:"isValid"`
	IsExcluded    int64  `json:"isExcluded"`
	ServiceType   int64  `json:"serviceType"`
	IsInherit     int64  `json:"isInherit"`
	DetailId      int64  `json:"detailId"`
	AllocData     string `json:"allocData"`
}

type BatchLeadsData struct {
	LeadsList []BatchLeadsInfo `json:"leadsList"`
}
